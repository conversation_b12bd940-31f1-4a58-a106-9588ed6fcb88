from app.service.generation_agent import create_agent_executor
from app.core.constant.prompts import *
from app.core.constant.prompts import (
    SM_part_five_chapter_fiften,
    SM_part_five_chapter_sixten,
)  # bug
from app.utils.md_and_pdf_kits import md_to_pdf
from langchain_core.messages import HumanMessage

import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv(".env")

# input = {"personalInfo":{"name":"林炯","major":"计算机科学与技术","currentYear":"大一","schoolName":"杭州电子科技大学"},"organizationInterests":["勤工俭学的机会与挑战"],"academicInterests":["专业分流的决策因素","实习的重要性与选择"],"furtherStudyInterests":["考研专业与院校选择"],"careerInterests":["职业定位与市场分析"]}


def map_interests_to_chapters(user_input):
    """
    将用户的兴趣列表映射到具体的章节。

    参数:
    user_input (dict): 包含用户兴趣的输入字典。

    返回:
    dict: 一个新的字典，其中每个兴趣点都已映射到其对应的章节信息。
    """

    # 1. 定义映射关系
    # 创建一个从“兴趣点”到“章节信息”的查找字典，以实现快速匹配。
    # 键 (key) 是兴趣点的字符串，值 (value) 是一个包含部分、章节和节信息的字典。
    interest_to_chapter_map = {
        # 第二部分：组织生活规划
        "入团意义与流程": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第二章：团学组织与政治生涯",
            "section": "一）入团意义与流程",
        },
        "入党申请与党员发展": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第二章：团学组织与政治生涯",
            "section": "二）入党申请与党员发展",
        },
        "学生组织与社团参与": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第二章：团学组织与政治生涯",
            "section": "三）学生组织与社团参与",
        },
        "入伍政策解读": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第三章：军事体验与服务",
            "section": "一）入伍政策解读",
        },
        "学工助理的角色与职责": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第四章：学生工作与社会实践",
            "section": "一）学工助理的角色与职责",
        },
        "勤工俭学的机会与挑战": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第四章：学生工作与社会实践",
            "section": "二）勤工俭学的机会与挑战",
        },
        "防诈骗攻略与安全意识": {
            "part": "# 第二部分：组织生活规划",
            "chapter": "第四章：学生工作与社会实践",
            "section": "三）防诈骗攻略与安全意识",
        },
        # 第三部分：学业规划
        "绩点的重要性与计算方法": {
            "part": "# 第三部分：学业规划",
            "chapter": "第五章：学业成绩与绩点提升",
            "section": "一）绩点的重要性与计算方法",
        },
        "提升绩点的策略与技巧": {
            "part": "# 第三部分：学业规划",
            "chapter": "第五章：学业成绩与绩点提升",
            "section": "二）提升绩点的策略与技巧",
        },
        "专业分流的决策因素": {
            "part": "# 第三部分：学业规划",
            "chapter": "第六章：专业选择与转换",
            "section": "一）专业分流的决策因素",
        },
        "转专业的流程与准备": {
            "part": "# 第三部分：学业规划",
            "chapter": "第六章：专业选择与转换",
            "section": "二）转专业的流程与准备",
        },
        "辅修的意义与选择": {
            "part": "# 第三部分：学业规划",
            "chapter": "第七章：辅修与第二学位",
            "section": "一）辅修的意义与选择",
        },
        "第二学位的规划与实施": {
            "part": "# 第三部分：学业规划",
            "chapter": "第七章：辅修与第二学位",
            "section": "二）第二学位的规划与实施",
        },
        "专业证书的重要性": {
            "part": "# 第三部分：学业规划",
            "chapter": "第八章：考证规划与职业发展",
            "section": "一）专业证书的重要性",
        },
        "考证规划的时间表与准备": {
            "part": "# 第三部分：学业规划",
            "chapter": "第八章：考证规划与职业发展",
            "section": "二）考证规划的时间表与准备",
        },
        "奖学金的种类与申请技巧": {
            "part": "# 第三部分：学业规划",
            "chapter": "第九章：奖学金规划与学术能力提升",
            "section": "一）奖学金的种类与申请技巧",
        },
        "竞赛的分类与选择": {
            "part": "# 第三部分：学业规划",
            "chapter": "第十章：竞赛规划与实践能力培养",
            "section": "一）竞赛的分类与选择",
        },
        "竞赛准备与经验分享": {
            "part": "# 第三部分：学业规划",
            "chapter": "第十章：竞赛规划与实践能力培养",
            "section": "二）竞赛准备与经验分享",
        },
        "实习的重要性与选择": {
            "part": "# 第三部分：学业规划",
            "chapter": "第十一章：实习规划与职业体验",
            "section": "一）实习的重要性与选择",
        },
        "实习申请流程与经验积累": {
            "part": "# 第三部分：学业规划",
            "chapter": "第十一章：实习规划与职业体验",
            "section": "二）实习申请流程与经验积累",
        },
        # 第四部分：升学规划
        "留学国家与院校选择": {
            "part": "# 第四部分：升学规划",
            "chapter": "第十二章：出国留学规划",
            "section": "一）留学国家与院校选择",
        },
        "留学申请流程与材料准备": {
            "part": "# 第四部分：升学规划",
            "chapter": "第十二章：出国留学规划",
            "section": "二）留学申请流程与材料准备",
        },
        "考研专业与院校选择": {
            "part": "# 第四部分：升学规划",
            "chapter": "第十三章：考研规划与策略",
            "section": "一）考研专业与院校选择",
        },
        "考研复习计划与时间管理": {
            "part": "# 第四部分：升学规划",
            "chapter": "第十三章：考研规划与策略",
            "section": "二）考研复习计划与时间管理",
        },
        "保研条件与申请流程": {
            "part": "# 第四部分：升学规划",
            "chapter": "第十四章：保研规划与准备",
            "section": "一）保研条件与申请流程",
        },
        "保研材料准备与面试技巧": {
            "part": "# 第四部分：升学规划",
            "chapter": "第十四章：保研规划与准备",
            "section": "二）保研材料准备与面试技巧",
        },
        # 第五部分：就业规划
        "公务员考试与事业单位考试概览": {
            "part": "# 第五部分：就业规划",
            "chapter": "第十五章：考公考编规划",
            "section": "一）公务员考试与事业单位考试概览",
        },
        "考公考编的备考策略与时间规划": {
            "part": "# 第五部分：就业规划",
            "chapter": "第十五章：考公考编规划",
            "section": "二）考公考编的备考策略与时间规划",
        },
        "职业定位与市场分析": {
            "part": "# 第五部分：就业规划",
            "chapter": "第十六章：求职规划与准备",
            "section": "一）职业定位与市场分析",
        },
        "求职材料准备与面试技巧": {
            "part": "# 第五部分：就业规划",
            "chapter": "第十六章：求职规划与准备",
            "section": "二）求职材料准备与面试技巧",
        },
        "简历撰写的基本原则与技巧": {
            "part": "# 第五部分：就业规划",
            "chapter": "第十七章：简历规划与个人品牌打造",
            "section": "一）简历撰写的基本原则与技巧",
        },
        "个人品牌建设与网络形象管理": {
            "part": "# 第五部分：就业规划",
            "chapter": "第十七章：简历规划与个人品牌打造",
            "section": "二）个人品牌建设与网络形象管理",
        },
    }

    # 2. 处理输入数据
    # 初始化一个空字典来存储结果
    processed_output = {}

    # 遍历输入字典中的每个键值对
    for category, interests in user_input.items():

        if isinstance(interests, list):

            mapped_interests = []
            # 遍历该类别下的每个兴趣点
            for interest in interests:
                # 在映射字典中查找兴趣点
                # 使用 .get() 方法可以避免当键不存在时程序抛出错误
                mapping_info = interest_to_chapter_map.get(interest, "未找到对应章节")

                mapped_interests.append({"interest": interest, "mapping": mapping_info})

            processed_output[category] = mapped_interests

    return processed_output


def grouped_by_chapter(mapped_data):
    grouped_data = {}
    for item in mapped_data.keys():
        for section in mapped_data[item]:
            chapter_data = section["mapping"]["chapter"]
            if chapter_data not in grouped_data:
                grouped_data[chapter_data] = []
            grouped_data[chapter_data].append(section)
    return grouped_data


def chapter_to_agent(chapter_data):
    """
    将章节字符串映射到预定义的系统提示词变量。

    参数:
    chapter_data (str): 章节信息字符串。

    返回:
    agent (AgentExecutor): 对应的AgentExecutor对象。
    """

    chapter_to_system_message_map = {
        # 第二部分：组织生活规划
        "第二章：团学组织与政治生涯": SM_part_two_chapter_two,
        "第三章：军事体验与服务": SM_part_two_chapter_three,
        "第四章：学生工作与社会实践": SM_part_two_chapter_four,
        # 第三部分：学业规划
        "第五章：学业成绩与绩点提升": SM_part_three_chapter_five,
        "第六章：专业选择与转换": SM_part_three_chapter_six,
        "第七章：辅修与第二学位": SM_part_three_chapter_seven,
        "第八章：考证规划与职业发展": SM_part_three_chapter_eight,
        "第九章：奖学金规划与学术能力提升": SM_part_three_chapter_nine,
        "第十章：竞赛规划与实践能力培养": SM_part_three_chapter_ten,
        "第十一章：实习规划与职业体验": SM_part_three_chapter_eleven,
        # 第四部分：升学规划
        "第十二章：出国留学规划": SM_part_four_chapter_twelve,
        "第十三章：考研规划与策略": SM_part_four_chapter_thirteen,
        "第十四章：保研规划与准备": SM_part_four_chapter_fourteen,
        # 第五部分：就业规划
        "第十五章：考公考编规划": SM_part_five_chapter_fiften,
        "第十六章：求职规划与准备": SM_part_five_chapter_sixten,
        "第十七章：简历规划与个人品牌打造": SM_part_five_chapter_seventeen,
    }

    system_prompt = chapter_to_system_message_map.get(
        chapter_data, DEFAULT_SYSTEM_MESSAGE
    )
    return create_agent_executor(system_prompt)


# output_data = map_interests_to_chapters(input)

# for item in output_data.keys():
#     for section in output_data[item]:
#         chapter_data = section["mapping"]["chapter"]


def generate_report_content(input):
    """
    生成报告内容
    :param input: 输入数据
    :return: 结构化报告内容字典
    """

    # get personal_info
    personal_info = "学生信息:"
    for key in input["personalInfo"].keys():
        personal_info += f"{key}: {input['personalInfo'][key]} "

    personal_info_chinese = input["personalInfo"]["name"]

    # print(SM_part_one_chapter_one)
    # 生成第一部分内容
    agent = create_agent_executor(SM_part_one_chapter_one)
    part_one_content = agent.invoke(
        {
            "messages": HumanMessage(
                content=f"这是学生信息{personal_info} 请根据年级生成内容，只生成大于等于当前年级的规划内容，确保结构完整，确保序号正确从“一）”开始，学校介绍与专业介绍部分可以详细一些，结点全览部分可以详细结合学生的专业与学校"
            )
        }
    )

    # 初始化结构化报告容器
    career_guide_report = {
        "part_one": {"title": "# 第一部分:学校专业介绍与节点全览", "content": ""},
        "part_two": {"title": "# 第二部分：组织生活规划", "chapters": {}},
        "part_three": {"title": "# 第三部分：学业规划", "chapters": {}},
        "part_four": {"title": "# 第四部分：升学规划", "chapters": {}},
        "part_five": {"title": "# 第五部分：就业规划", "chapters": {}},
    }

    # 第一部分内容
    # career_guide_report["part_one"]["content"] = part_one_content["messages"][1].content
    # if part_one_content["messages"][1].content == "":
    #     print(part_one_content["messages"][3].content)
    #     career_guide_report["part_one"]["content"] = part_one_content["messages"][3].content
    # else:
    career_guide_report["part_one"]["content"] = part_one_content["messages"][-1].content

    mapped_data = map_interests_to_chapters(input)
    grouped_data = grouped_by_chapter(mapped_data)

    # 使用 ThreadPoolExecutor 并行处理章节
    from concurrent.futures import ThreadPoolExecutor, as_completed

    # 重构后的并行处理逻辑
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = {}

        # 准备所有任务
        for chapter in grouped_data.keys():
            # 提前获取章节元数据

            part_mapping = {
                "# 第二部分：组织生活规划": "part_two",
                "# 第三部分：学业规划": "part_three",
                "# 第四部分：升学规划": "part_four",
                "# 第五部分：就业规划": "part_five",
            }

            part_title = grouped_data[chapter][0]["mapping"]["part"]
            part_key = part_mapping.get(part_title, "part_one")

            # 准备章节内容
            section_data = " ".join(
                s["mapping"]["section"] for s in grouped_data[chapter]
            )
            agent = chapter_to_agent(chapter)
            HM_content = f"{personal_info} 需要生成{section_data}的内容，确保结构完整，确保序号正确从1开始。使用搜索工具search_tool搜索时，无需输出工具内容。不要把web搜索内容直接放到内容中，也不要把注释放到内容中。"

            # 提交任务到线程池
            future = executor.submit(
                agent.invoke, {"messages": [HumanMessage(content=HM_content)]}
            )
            futures[future] = (part_key, chapter)

        # 并行处理结果
        for future in as_completed(futures):
            part_key, chapter = futures[future]
            try:
                response = future.result()
                generated_content = response["messages"][-1].content

                # 存储到报告结构
                if part_key != "part_one":
                    career_guide_report[part_key]["chapters"][chapter] = {
                        "content": generated_content
                    }
            except Exception as e:
                print(f"处理章节 {chapter} 时发生错误: {str(e)}")

    return career_guide_report, personal_info_chinese


def save_to_md(report_data, filename=None):
    """
    将报告内容保存为单个Markdown文件
    :param report_data: generate_report_content返回的字典数据
    :param filename: 输出文件名（可选，默认自动生成带时间戳的文件名）
    :return: 生成的文件路径
    """

    # 生成带时间戳的文件名
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"career_guide_{timestamp}_without_homepage.md"

    # 创建reports目录（如果不存在）
    os.makedirs("reports", exist_ok=True)
    filepath = os.path.join("reports", filename)

    with open(filepath, "w", encoding="utf-8") as f:
        # 写入第一部分
        f.write("[TOC]\n")
        f.write(f"{report_data['part_one']['title']}\n\n")
        f.write(f"{report_data['part_one']['content']}\n\n")

        # 写入其他部分
        for part_key in ["part_two", "part_three", "part_four", "part_five"]:
            part = report_data[part_key]
            f.write(f"\n{part['title']}\n")

            for chapter_name, chapter_content in part["chapters"].items():
                f.write(f"{chapter_content['content']}\n")

    return filepath


def report_generation_workflow(input):
    report_data, personal_info = generate_report_content(input)
    md_path = save_to_md(report_data)
    pdf_path = md_path[: len(md_path) - 2] + "pdf"
    final_pdf_path = md_path[: len(md_path) - 20] + ".pdf"
    md_to_pdf(md_path, pdf_path, final_pdf_path, personal_info)
    return final_pdf_path


# report_generation_workflow(input)
