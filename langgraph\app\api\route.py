from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.api.routes import ai

api_router = APIRouter()
api_router.include_router(ai.router, tags=["ai"])

# 健康检查端点
@api_router.get("/health")
async def health_check():
    """健康检查端点"""
    return JSONResponse(
        status_code=200,
        content={"status": "healthy", "message": "LangGraph Career Guide API is running"}
    )
