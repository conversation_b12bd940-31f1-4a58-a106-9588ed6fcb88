import markdown
import pdfkit

from PyPDF2 import PdfReader, PdfWriter

from PIL import Image, ImageDraw, ImageFont

import img2pdf

def add_text_to_image(image_path, text, output_path):
    # 打开原始图片
    img = Image.open(image_path)
    
    # 创建绘图对象
    draw = ImageDraw.Draw(img)
    
    # 设置字体（确保系统有该字体）
    font = ImageFont.truetype('Hiragino-Sans-GB.ttc', size=60)
    
    # 在图片右下角添加文字
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = (text_bbox[2] - text_bbox[0]) * -1
    text_height = (text_bbox[3] - text_bbox[1]) * -1
    x = img.width + text_width - 2130
    y = img.height + text_height - 2700
    draw.text((x, y), text, font=font, fill='black')
    # 保存新图片前转换为RGB模式
    if img.mode in ('RGBA', 'LA'):
        img = img.convert('RGB')
    img.save(output_path, "JPEG", quality=95)
  

def md_to_pdf(
    input_md: str,
    output_pdf: str,
    final_output_pdf: str,
    personal_info: str,
    wkhtmltopdf_path: str | None = None,
    font_size: int = 24,
) -> None:
    """
    把 Markdown 转成 PDF（基于 wkhtmltopdf）。
    参数:
        input_md:      输入的 Markdown 文件路径
        output_pdf:    输出的 PDF 文件路径
        final_output_pdf: 最终输出的 PDF 文件路径
        wkhtmltopdf_path: 可选，wkhtmltopdf 可执行文件路径（默认自动查找）
        font_size:     字体大小，默认24px
    """

    if wkhtmltopdf_path:
        config = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)
    else:
        config = None

    # 1. 读 Markdown
    with open(input_md, "r", encoding="utf-8") as f:
        md_text = f.read()

    html_body = markdown.markdown(
        md_text, output_format="html", extensions=["extra", "toc"]
    )


    html = f"""<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="utf-8">
  <title>文档标题</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown.min.css">
  <!--<link rel="stylesheet" href="styles.css">-->
  <script src="https://c.webfontfree.com/c.js?f=SimSun" type="text/javascript"></script>
  <style>

    body {{ box-sizing: border-box; padding: 40px; font-size: {font_size}px; font-family: '宋体', 'SimSun', sans-serif !important;}}
    .markdown-body {{ max-width: 800px; margin: auto; }}
    .toc {{ margin-bottom: 40px; page-break-after: always;}}
    /* 设置各级标题的字体大小 */
    h1 {{ font-size: {font_size * 1.8}px; }}
    h2 {{ font-size: {font_size * 1.5}px; }}
    h3 {{ font-size: {font_size * 4.0}px; }}
    h4 {{ font-size: {font_size * 1.1}px; }}
    h5 {{ font-size: {font_size}px; }}
    h6 {{ font-size: {font_size * 0.9}px; }}
    /* 设置段落和列表的字体大小 */
    p, li, td, th {{ font-size: {font_size}px; }}
  </style>
</head>
<body class="markdown-body">
  <div class="toc">

  </div>
  <div>
  {html_body}  <!-- 正文内容 -->
  </div>
</body>
</html>"""

    add_text_to_image("images/home_page.jpg", personal_info, "images/"+final_output_pdf[8:-3]+'png')
    # 图片转换为PDF

    a4inpt = (img2pdf.in_to_pt(8.27), img2pdf.in_to_pt(11.69))  # A4 尺寸（宽 x 高）以 pt 为单位
    layout_fun = img2pdf.get_layout_fun(pagesize=a4inpt)

    with open("images/"+final_output_pdf[8:-3]+'png', 'rb') as img_file:
        img_pdf = img2pdf.convert(img_file, layout_fun=layout_fun, fit=img2pdf.FitMode.into)
        with open(final_output_pdf[:-4]+"_homepage.pdf", 'wb') as pdf_file:
            pdf_file.write(img_pdf)
    pdfkit.from_string(html, output_pdf, configuration=config)

    existing_pdf = PdfReader(output_pdf)
    # person_info_pdf = PdfReader(info_page_html)
    custom_pdf = PdfReader(final_output_pdf[:-4]+"_homepage.pdf")
    pdf_writer = PdfWriter()
    pdf_writer.add_page(custom_pdf.pages[0])
    # pdf_writer.add_page(person_info_pdf.pages[0])
    for page in existing_pdf.pages[1:]:
      pdf_writer.add_page(page)

    with open(final_output_pdf, "wb") as output:
      pdf_writer.write(output)


