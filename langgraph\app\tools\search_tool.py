from langchain_core.tools import tool
from pydantic import BaseModel, Field

from zhipuai import ZhipuAI

from dotenv import load_dotenv
import os

load_dotenv(".env")

class SearchToolInput(BaseModel):
    query: str = Field(description="搜索词，搜索查询的语句")

@tool("search_tool",description="search_tool(query: str) -> str - Searches the API for the query.",args_schema=SearchToolInput,return_direct=False)
def search_tool(query: str):
    """搜索工具，用于搜索相关内容

    Args:
        query: 搜索词，搜索查询的语句

    """

    client = ZhipuAI(api_key=os.getenv("OPENAI_API_KEY"))
    response = client.web_search.web_search(
        search_engine="search_pro",
        search_query=query,
        count=5,  # 返回结果的条数，范围1-50，默认10
        search_recency_filter="noLimit",  # 搜索指定日期范围内的内容
        content_size="high",  # 控制网页摘要的字数，默认medium
    )

    results = "web搜索结果："

    if response.search_result is None:
        return results

    for item in response.search_result:
        results += f"【{item.content}】"  # type: ignore
    # print(results)
    return results

# print(search_tool.name)
# print(search_tool.description)
# print(search_tool.args)
