from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from app.schemas.chat import ReportGenerationRequest
from app.service.workflow.report_generation_workflow import report_generation_workflow
import logging
import os


# 配置日志
logging.basicConfig(level=logging.INFO)

router = APIRouter()


@router.post("/generate-report")
async def generate_report(request: ReportGenerationRequest):
    """
    生成职业规划报告PDF文件
    """
    try:
        # 将请求转换为字典格式，匹配workflow函数的期望输入
        input_data = {
            "personalInfo": {
                "name": request.personalInfo.name,
                "major": request.personalInfo.major,
                "currentYear": request.personalInfo.currentYear,
                "schoolName": request.personalInfo.schoolName,
            },
            "organizationInterests": request.organizationInterests,
            "academicInterests": request.academicInterests,
            "furtherStudyInterests": request.furtherStudyInterests,
            "careerInterests": request.careerInterests,
        }

        # 调用workflow生成PDF
        pdf_path = report_generation_workflow(input_data)

        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            raise HTTPException(status_code=500, detail="PDF文件生成失败")

        # 返回PDF文件
        return FileResponse(
            path=pdf_path,
            media_type="application/pdf",
            filename=f"career_guide_{request.personalInfo.name}.pdf",
        )

    except Exception as e:
        logging.error(f"生成报告时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"生成报告时发生内部错误: {str(e)}")
