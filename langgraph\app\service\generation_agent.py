from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langgraph.prebuilt import create_react_agent
from langchain_community.chat_models import ChatZhipuAI
from langchain_core.messages import SystemMessage

from app.core.ai_config import ai_settings
from app.tools.search_tool import search_tool


def create_agent_executor(system_prompt: SystemMessage):

    # 初始化模型，使用配置中的环境变量
    llm = ChatZhipuAI(
        model=ai_settings.OPENAI_MODEL,
        temperature=1.0,
    )

    # 工具
    tools = [search_tool]

    # 提示词模板
    prompt = ChatPromptTemplate.from_messages(
        [system_prompt, MessagesPlaceholder(variable_name="messages")]
    )

    # 创建可执行的图
    agent_graph = create_react_agent(llm, tools, prompt=prompt)

    return agent_graph
