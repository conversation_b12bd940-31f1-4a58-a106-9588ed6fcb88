services:
  langgraph-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: langgraph-career-guide
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
    volumes:
      # 挂载生成的图片和报告目录，便于持久化存储
      - ./images:/app/images
      - ./reports:/app/reports
      # 如果有 .env 文件，可以挂载进去
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: langgraph-network
